import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  FiDownload,
  FiStar,
  FiCalendar,
  FiExternalLink,
  FiRefreshCw,
  FiEdit,
  FiTrash2,
  FiMessageCircle,
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiTrendingUp,
  FiUsers,
} from "react-icons/fi";
import PluginIcon from "../Common/PluginIcon";
import {
  formatNumber,
  formatRelativeTime,
  getStatusBadgeColor,
  getPluginBannerUrl,
} from "../../utils/formatters";

const PluginCard = ({
  plugin,
  onRefresh,
  onEdit,
  onDelete,
  isRefreshing = false,
}) => {
  const [imageError, setImageError] = useState(false);

  const bannerUrl = getPluginBannerUrl(plugin);

  // Helper function to extract date and calculate days
  const getLastReleaseInfo = (plugin) => {
    // Try to get from new fields first
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0]; // Get YYYY-MM-DD format
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Try to extract from lastUpdated field (WordPress.org format)
    if (plugin.lastUpdated) {
      try {
        // Extract date from WordPress.org format like "2025-05-14 10:31am GMT"
        const match = plugin.lastUpdated.match(/(\d{4}-\d{2}-\d{2})/);
        if (match) {
          const dateStr = match[1]; // This gives us "2025-05-14"
          const date = new Date(dateStr + "T00:00:00.000Z");
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    return { date: null, days: 0 };
  };

  const releaseInfo = getLastReleaseInfo(plugin);

  return (
    <div className="plugin-card bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200">
      {/* Plugin Banner/Header */}
      <div className="relative h-32 bg-gradient-to-r from-primary-500 to-primary-600 overflow-hidden">
        {bannerUrl && !imageError ? (
          <img
            src={bannerUrl}
            alt={`${plugin.name} banner`}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : null}
        <div
          className={`absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 flex items-center justify-center ${
            bannerUrl && !imageError ? "hidden" : "block"
          }`}
        >
          <div className="text-center text-white">
            <PluginIcon
              plugin={plugin}
              size="md"
              className="mx-auto mb-2 bg-white/20 backdrop-blur-sm"
            />
            <h3 className="font-medium text-sm truncate px-4">{plugin.name}</h3>
          </div>
        </div>

        {/* Status Badge */}
        <div className="absolute top-3 right-3">
          <span
            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ${getStatusBadgeColor(
              plugin.status
            )}`}
          >
            {plugin.status || "unknown"}
          </span>
        </div>
      </div>

      {/* Plugin Content */}
      <div className="p-3">
        {/* Plugin Info */}
        <div className="flex items-start space-x-2 mb-3">
          <PluginIcon plugin={plugin} size="sm" className="mt-0.5" />
          <div className="flex-1 min-w-0">
            <h3
              className="font-semibold text-gray-900 truncate text-sm"
              title={plugin.name}
            >
              {plugin.name}
            </h3>
            <p className="text-xs text-gray-500 truncate" title={plugin.slug}>
              {plugin.slug}
            </p>
            {plugin.shortDescription && (
              <p
                className="text-xs text-gray-600 mt-1 line-clamp-2"
                title={plugin.shortDescription}
              >
                {plugin.shortDescription}
              </p>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="space-y-2 mb-3">
          {/* Downloads & Active Installs */}
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center text-xs">
              <FiDownload className="w-3 h-3 text-blue-500 mr-1.5" />
              <div>
                <span className="font-medium text-gray-900">
                  {formatNumber(plugin.downloads)}
                </span>
                <div className="text-xs text-gray-500">total downloads</div>
              </div>
            </div>
            <div className="flex items-center text-xs">
              <FiUsers className="w-3 h-3 text-green-500 mr-1.5" />
              <div>
                <span className="font-medium text-gray-900">
                  {formatNumber(plugin.activeInstalls || 0)}
                </span>
                <div className="text-xs text-gray-500">active installs</div>
              </div>
            </div>
          </div>

          {/* Rating & Rank */}
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center text-xs">
              <FiStar className="w-3 h-3 text-yellow-500 mr-1.5" />
              <div>
                <span className="font-medium text-gray-900">
                  {plugin.rating || "0.0"}
                </span>
                <div className="text-xs text-gray-500">
                  ({plugin.ratingCount || 0} reviews)
                </div>
              </div>
            </div>
            <div className="flex items-center text-xs">
              <FiTrendingUp className="w-3 h-3 text-purple-500 mr-1.5" />
              <div>
                <span className="font-medium text-gray-900">
                  #{plugin.pluginRank || "N/A"}
                </span>
                <div className="text-xs text-gray-500">plugin rank</div>
              </div>
            </div>
          </div>

          {/* Last Release Date */}
          <div className="flex items-center text-sm">
            <FiCalendar className="w-4 h-4 text-green-500 mr-2" />
            <div>
              <span className="font-medium text-gray-900">
                {releaseInfo.date || "Never"}
              </span>
              <div className="text-xs text-gray-500">
                {releaseInfo.date && releaseInfo.days > 0
                  ? `${releaseInfo.days} days ago`
                  : "last release"}
              </div>
            </div>
          </div>

          {/* Support Topics */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <FiMessageCircle className="w-4 h-4 text-purple-500 mr-2" />
              <span className="text-sm font-medium text-gray-900">
                Support Topics
              </span>
            </div>
            <a
              href={`https://wordpress.org/support/plugin/${plugin.slug}/unresolved/`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-xs hover:bg-gray-100 rounded p-1 transition-colors"
            >
              <FiXCircle className="w-3 h-3 text-red-500 mr-1" />
              <span className="font-medium text-gray-900">
                {plugin.supportTickets?.unresolvedTopics || 0}
              </span>
              <span className="text-gray-500 ml-1">unresolved</span>
              <FiExternalLink className="w-3 h-3 text-gray-400 ml-1" />
            </a>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <Link
            to={`/plugins/${plugin.slug}`}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center"
          >
            <FiExternalLink className="w-4 h-4 mr-1" />
            View Details
          </Link>

          <div className="flex items-center space-x-1">
            <button
              onClick={() => onRefresh(plugin)}
              disabled={isRefreshing}
              className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200 disabled:opacity-50"
              title="Refresh Data"
            >
              <FiRefreshCw
                className={`w-4 h-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
            </button>
            <button
              onClick={() => onEdit(plugin)}
              className="p-1.5 text-yellow-600 hover:bg-yellow-50 rounded-md transition-colors duration-200"
              title="Edit Plugin"
            >
              <FiEdit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(plugin)}
              className="p-1.5 text-red-600 hover:bg-red-50 rounded-md transition-colors duration-200"
              title="Delete Plugin"
            >
              <FiTrash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PluginCard;
