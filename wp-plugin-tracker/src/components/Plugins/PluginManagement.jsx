import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  FiPlus,
  FiSearch,
  FiEdit,
  FiTrash2,
  FiPackage,
  FiDownload,
  FiStar,
  FiRefreshCw,
  FiExternalLink,
  FiCalendar,
  FiGrid,
  FiList,
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiTrendingUp,
  FiUsers,
  FiMessageCircle,
} from "react-icons/fi";
import { pluginsAPI } from "../../services/api";
import PluginModal from "./PluginModal";
import PluginCard from "./PluginCard";
import DeleteConfirmModal from "../Common/DeleteConfirmModal";
import PluginIcon from "../Common/PluginIcon";
import { formatNumber, formatRelativeTime } from "../../utils/formatters";
import toast from "react-hot-toast";

const PluginManagement = () => {
  const [plugins, setPlugins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showPluginModal, setShowPluginModal] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [pluginToDelete, setPluginToDelete] = useState(null);
  const [refreshingPlugins, setRefreshingPlugins] = useState(new Set());
  const [viewMode, setViewMode] = useState("cards"); // "cards" or "table"
  const [stats, setStats] = useState({
    totalPlugins: 0,
    totalDownloads: 0,
    averageRating: 0,
    totalTickets: 0,
  });

  // Helper function to extract date and calculate days
  const getLastReleaseInfo = (plugin) => {
    // Try to get from new fields first
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0]; // Get YYYY-MM-DD format
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Try to extract from lastUpdated field (WordPress.org format)
    if (plugin.lastUpdated) {
      try {
        // Extract date from WordPress.org format like "2025-05-14 10:31am GMT"
        const match = plugin.lastUpdated.match(/(\d{4}-\d{2}-\d{2})/);
        if (match) {
          const dateStr = match[1]; // This gives us "2025-05-14"
          const date = new Date(dateStr + "T00:00:00.000Z");
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    return { date: null, days: 0 };
  };

  useEffect(() => {
    fetchPlugins();
    fetchStats();
  }, []);

  const fetchPlugins = async () => {
    try {
      const response = await pluginsAPI.getPlugins({ search: searchTerm });

      // Handle both mock API and real API response formats
      const data = response.data || response;
      const pluginsList = data.data || data.plugins || data || [];
      setPlugins(pluginsList);
    } catch (error) {
      console.error("Error fetching plugins:", error);
      toast.error("Failed to fetch plugins");
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await pluginsAPI.getPluginStats();
      setStats(response.data);
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleAddPlugin = () => {
    setSelectedPlugin(null);
    setShowPluginModal(true);
  };

  const handleEditPlugin = (plugin) => {
    setSelectedPlugin(plugin);
    setShowPluginModal(true);
  };

  const handleDeletePlugin = (plugin) => {
    setPluginToDelete(plugin);
    setShowDeleteModal(true);
  };

  const handleRefreshPlugin = async (plugin) => {
    setRefreshingPlugins((prev) => new Set(prev).add(plugin._id));

    try {
      const response = await pluginsAPI.refreshPluginData(plugin._id);
      setPlugins(
        plugins.map((p) => (p._id === plugin._id ? response.data.plugin : p))
      );
      toast.success("Plugin data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing plugin:", error);
      toast.error("Failed to refresh plugin data");
    } finally {
      setRefreshingPlugins((prev) => {
        const newSet = new Set(prev);
        newSet.delete(plugin._id);
        return newSet;
      });
    }
  };

  const confirmDelete = async () => {
    try {
      await pluginsAPI.deletePlugin(pluginToDelete._id);
      setPlugins(plugins.filter((p) => p._id !== pluginToDelete._id));
      toast.success("Plugin deleted successfully");
    } catch (error) {
      console.error("Error deleting plugin:", error);
      toast.error("Failed to delete plugin");
    } finally {
      setShowDeleteModal(false);
      setPluginToDelete(null);
    }
  };

  const handlePluginSaved = (savedPlugin) => {
    if (selectedPlugin) {
      // Update existing plugin
      setPlugins(
        plugins.map((p) => (p._id === savedPlugin._id ? savedPlugin : p))
      );
    } else {
      // Add new plugin
      setPlugins([...plugins, savedPlugin]);
    }
    setShowPluginModal(false);
    setSelectedPlugin(null);
  };

  const filteredPlugins = plugins.filter(
    (plugin) =>
      plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plugin.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const StatCard = ({ title, value, icon: Icon, color, change }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600">{title}</p>
          <p className="text-lg font-bold text-gray-900">{value}</p>
          {change && (
            <p
              className={`text-xs flex items-center mt-0.5 ${
                change > 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              <FiTrendingUp className="w-3 h-3 mr-1" />
              {change > 0 ? "+" : ""}
              {change}%
            </p>
          )}
        </div>
        <div className={`p-2 rounded-full ${color}`}>
          <Icon className="w-4 h-4 text-white" />
        </div>
      </div>
    </div>
  );

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "closed":
        return "bg-red-100 text-red-800";
      case "disabled":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Plugin Management</h1>
          <p className="text-sm text-gray-600">
            Track and manage WordPress plugins
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {/* View Toggle */}
          <div className="flex items-center bg-gray-100 rounded-md p-0.5">
            <button
              onClick={() => setViewMode("cards")}
              className={`p-1.5 rounded-sm transition-colors duration-200 ${
                viewMode === "cards"
                  ? "bg-white text-primary-600 shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              title="Card View"
            >
              <FiGrid className="w-3.5 h-3.5" />
            </button>
            <button
              onClick={() => setViewMode("table")}
              className={`p-1.5 rounded-sm transition-colors duration-200 ${
                viewMode === "table"
                  ? "bg-white text-primary-600 shadow-sm"
                  : "text-gray-500 hover:text-gray-700"
              }`}
              title="Table View"
            >
              <FiList className="w-3.5 h-3.5" />
            </button>
          </div>
          <button
            onClick={handleAddPlugin}
            className="btn-primary flex items-center px-3 py-2 text-sm"
          >
            <FiPlus className="w-3.5 h-3.5 mr-1.5" />
            Add Plugin
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Plugins"
          value={stats.totalPlugins}
          icon={FiPackage}
          color="bg-blue-500"
        />
        <StatCard
          title="Total Downloads"
          value={stats.totalDownloads?.toLocaleString() || "0"}
          icon={FiDownload}
          color="bg-green-500"
        />
        <StatCard
          title="Average Rating"
          value={stats.averageRating?.toFixed(1) || "0.0"}
          icon={FiStar}
          color="bg-yellow-500"
        />
        <StatCard
          title="Support Tickets"
          value={stats.totalTickets}
          icon={FiMessageCircle}
          color="bg-purple-500"
        />
      </div>

      {/* Search Bar */}
      <div className="flex justify-between items-center">
        <div className="input-group max-w-md">
          <div className="input-icon">
            <FiSearch />
          </div>
          <input
            type="text"
            placeholder="Search plugins by name or slug..."
            value={searchTerm}
            onChange={handleSearch}
            className="search-input"
          />
        </div>
      </div>

      {/* Plugins Display */}
      {viewMode === "table" ? (
        /* Table View */
        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell table-cell-plugin">Plugin</th>
                <th className="table-header-cell table-cell-downloads">
                  Downloads
                </th>
                <th className="table-header-cell table-cell-installs">
                  Active Installs
                </th>
                <th className="table-header-cell table-cell-rank">Rank</th>
                <th className="table-header-cell table-cell-rating">Rating</th>
                <th className="table-header-cell table-cell-status">Status</th>
                <th className="table-header-cell table-cell-tickets">
                  Unresolved Topics
                </th>
                <th className="table-header-cell table-cell-date">
                  Last Release
                </th>
                <th className="table-header-cell table-cell-actions">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredPlugins.map((plugin) => (
                <tr key={plugin._id} className="table-row">
                  <td className="table-cell table-cell-plugin">
                    <div className="flex items-center min-w-0">
                      <PluginIcon plugin={plugin} size="sm" className="mr-3" />
                      <div className="min-w-0 flex-1">
                        <div
                          className="font-medium text-gray-900 truncate"
                          title={plugin.name}
                        >
                          {plugin.name}
                        </div>
                        <div
                          className="text-sm text-gray-500 truncate"
                          title={plugin.slug}
                        >
                          {plugin.slug}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell table-cell-downloads">
                    <div className="flex items-center">
                      <FiDownload className="w-3 h-3 text-blue-500 mr-1 flex-shrink-0" />
                      <div>
                        <span className="text-gray-900 font-medium">
                          {plugin.downloads >= 1000000
                            ? (plugin.downloads / 1000000).toFixed(1) + "M"
                            : plugin.downloads >= 1000
                            ? (plugin.downloads / 1000).toFixed(1) + "K"
                            : plugin.downloads?.toLocaleString() || "0"}
                        </span>
                        <div className="text-xs text-gray-500">
                          total downloads
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell table-cell-installs">
                    <div className="flex items-center">
                      <FiUsers className="w-3 h-3 text-green-500 mr-1 flex-shrink-0" />
                      <div>
                        <span className="text-gray-900 font-medium">
                          {formatNumber(plugin.activeInstalls || 0)}
                        </span>
                        <div className="text-xs text-gray-500">
                          active installs
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell table-cell-rank">
                    <div className="flex items-center">
                      <FiTrendingUp className="w-3 h-3 text-purple-500 mr-1 flex-shrink-0" />
                      <div>
                        <span className="text-gray-900 font-medium">
                          #{plugin.pluginRank || "N/A"}
                        </span>
                        <div className="text-xs text-gray-500">rank</div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell table-cell-rating">
                    <div className="flex items-center">
                      <FiStar className="w-3 h-3 text-yellow-400 mr-1 flex-shrink-0" />
                      <span className="text-gray-900 truncate">
                        {plugin.rating || "0.0"} ({plugin.ratingCount || 0})
                      </span>
                    </div>
                  </td>
                  <td className="table-cell table-cell-status">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full capitalize ${getStatusBadgeColor(
                        plugin.status
                      )}`}
                    >
                      {plugin.status || "unknown"}
                    </span>
                  </td>
                  <td className="table-cell table-cell-tickets">
                    <a
                      href={`https://wordpress.org/support/plugin/${plugin.slug}/unresolved/`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-xs hover:bg-gray-100 rounded p-1 transition-colors"
                    >
                      <FiXCircle className="w-3 h-3 text-red-500 mr-1" />
                      <span className="font-medium text-gray-900">
                        {plugin.supportTickets?.unresolvedTopics || 0}
                      </span>
                      <span className="text-gray-500 ml-1">unresolved</span>
                      <FiExternalLink className="w-3 h-3 text-gray-400 ml-1" />
                    </a>
                  </td>
                  <td className="table-cell table-cell-date">
                    <div className="flex items-center">
                      <FiCalendar className="w-3 h-3 text-green-500 mr-1 flex-shrink-0" />
                      <div>
                        {(() => {
                          const releaseInfo = getLastReleaseInfo(plugin);
                          return (
                            <>
                              <span className="text-gray-900 text-xs font-medium">
                                {releaseInfo.date || "Never"}
                              </span>
                              <div className="text-xs text-gray-500">
                                {releaseInfo.date && releaseInfo.days > 0
                                  ? `${releaseInfo.days} days ago`
                                  : "last release"}
                              </div>
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  </td>
                  <td className="table-cell table-cell-actions">
                    <div className="flex items-center space-x-1">
                      <Link
                        to={`/plugins/${plugin.slug}`}
                        className="p-1.5 text-green-600 hover:bg-green-50 rounded-md transition-colors duration-200"
                        title="View Details"
                      >
                        <FiExternalLink className="w-3.5 h-3.5" />
                      </Link>
                      <button
                        onClick={() => handleRefreshPlugin(plugin)}
                        disabled={refreshingPlugins.has(plugin._id)}
                        className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200 disabled:opacity-50"
                        title="Refresh Data"
                      >
                        <FiRefreshCw
                          className={`w-3.5 h-3.5 ${
                            refreshingPlugins.has(plugin._id)
                              ? "animate-spin"
                              : ""
                          }`}
                        />
                      </button>
                      <button
                        onClick={() => handleEditPlugin(plugin)}
                        className="p-1.5 text-yellow-600 hover:bg-yellow-50 rounded-md transition-colors duration-200"
                        title="Edit Plugin"
                      >
                        <FiEdit className="w-3.5 h-3.5" />
                      </button>
                      <button
                        onClick={() => handleDeletePlugin(plugin)}
                        className="p-1.5 text-red-600 hover:bg-red-50 rounded-md transition-colors duration-200"
                        title="Delete Plugin"
                      >
                        <FiTrash2 className="w-3.5 h-3.5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        /* Card View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredPlugins.map((plugin) => (
            <PluginCard
              key={plugin._id}
              plugin={plugin}
              onRefresh={handleRefreshPlugin}
              onEdit={handleEditPlugin}
              onDelete={handleDeletePlugin}
              isRefreshing={refreshingPlugins.has(plugin._id)}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {filteredPlugins.length === 0 && (
        <div className="text-center py-8">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
            <FiPackage className="w-5 h-5 text-gray-400" />
          </div>
          <p className="text-gray-500 text-sm">No plugins found</p>
          <button
            onClick={handleAddPlugin}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium mt-2"
          >
            Add your first plugin
          </button>
        </div>
      )}

      {/* Plugin Modal */}
      {showPluginModal && (
        <PluginModal
          plugin={selectedPlugin}
          onClose={() => {
            setShowPluginModal(false);
            setSelectedPlugin(null);
          }}
          onSave={handlePluginSaved}
        />
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <DeleteConfirmModal
          title="Delete Plugin"
          message={`Are you sure you want to delete ${pluginToDelete?.name}? This action cannot be undone.`}
          onConfirm={confirmDelete}
          onCancel={() => {
            setShowDeleteModal(false);
            setPluginToDelete(null);
          }}
        />
      )}
    </div>
  );
};

export default PluginManagement;
