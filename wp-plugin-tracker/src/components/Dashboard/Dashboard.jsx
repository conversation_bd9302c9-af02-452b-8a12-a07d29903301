import React from "react";
import { <PERSON> } from "react-router-dom";
import { FiPlus, FiBarChart2, FiT<PERSON>ding<PERSON>p, FiCalendar } from "react-icons/fi";
import { useAuth } from "../../contexts/AuthContext";
import PluginDashboardTable from "./PluginDashboardTable";
import PluginPerformanceCards from "./PluginPerformanceCards";
import DownloadTracking from "./DownloadTracking";

const Dashboard = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 border border-blue-100">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1">
              Plugin Dashboard
            </h1>
            <p className="text-sm text-gray-600">
              Welcome back,{" "}
              <span className="font-semibold text-blue-700">{user?.name}</span>!
              👋
              <span className="ml-2 text-gray-500">
                Monitor your WordPress plugins in real-time
              </span>
            </p>
          </div>
          <Link
            to="/plugins"
            className="btn-primary flex items-center px-4 py-2 text-sm font-medium"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            Add Plugin
          </Link>
        </div>
      </div>

      {/* Plugin Performance Dashboard - HIDDEN */}
      {/* <div className="mb-6">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 lg:col-span-8">
            <PluginDashboardTable />
          </div>
        </div>
      </div> */}

      {/* Plugin Performance Cards - Full width */}
      <div className="mb-6">
        <PluginPerformanceCards />
      </div>

      {/* Download Tracking - HIDDEN */}
      {/* <div className="w-full">
        <DownloadTracking />
      </div> */}
    </div>
  );
};

export default Dashboard;
