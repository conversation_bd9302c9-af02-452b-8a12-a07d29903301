import React from "react";
import { <PERSON> } from "react-router-dom";
import { FiPlus, FiBarChart2, FiT<PERSON>ding<PERSON>p, FiCalendar } from "react-icons/fi";
import { useAuth } from "../../contexts/AuthContext";
import PluginDashboardTable from "./PluginDashboardTable";
import PluginPerformanceCards from "./PluginPerformanceCards";
import DownloadTracking from "./DownloadTracking";

const Dashboard = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Plugin Dashboard
            </h1>
            <p className="text-lg text-gray-600 mb-1">
              Welcome back,{" "}
              <span className="font-semibold text-blue-700">{user?.name}</span>!
              👋
            </p>
            <p className="text-sm text-gray-500">
              Monitor and manage your WordPress plugin performance in real-time
            </p>
          </div>
          <div className="flex flex-col items-end space-y-2">
            <Link
              to="/plugins"
              className="btn-primary flex items-center px-6 py-3 text-sm font-medium"
            >
              <FiPlus className="w-4 h-4 mr-2" />
              Add New Plugin
            </Link>
            <p className="text-xs text-gray-500">
              {new Date().toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Plugin Performance Dashboard - HIDDEN */}
      {/* <div className="mb-6">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 lg:col-span-8">
            <PluginDashboardTable />
          </div>
        </div>
      </div> */}

      {/* Quick Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FiBarChart2 className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Active Plugins
              </p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <FiTrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">
                Total Downloads
              </p>
              <p className="text-2xl font-bold text-gray-900">2.4M+</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <FiCalendar className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Last Updated</p>
              <p className="text-2xl font-bold text-gray-900">Today</p>
            </div>
          </div>
        </div>
      </div>

      {/* Plugin Performance Cards - Full width */}
      <div className="mb-6">
        <PluginPerformanceCards />
      </div>

      {/* Download Tracking - HIDDEN */}
      {/* <div className="w-full">
        <DownloadTracking />
      </div> */}
    </div>
  );
};

export default Dashboard;
