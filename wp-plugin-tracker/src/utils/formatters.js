/**
 * Format large numbers with K/M suffixes
 * @param {number} num - The number to format
 * @returns {string} - Formatted number string
 */
export const formatNumber = (num) => {
  if (!num || num === 0) return "0";

  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toLocaleString();
};

/**
 * Format numbers without abbreviation (show real numbers)
 * @param {number} num - The number to format
 * @returns {string} - Formatted number string with commas
 */
export const formatRealNumber = (num) => {
  if (!num || num === 0) return "0";
  return num.toLocaleString();
};

/**
 * Format date to relative time (e.g., "2 days ago")
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted relative time string
 */
export const formatRelativeTime = (date) => {
  if (!date) return "Never";

  const now = new Date();
  const targetDate = new Date(date);
  const diffTime = Math.abs(now - targetDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) return "Today";
  if (diffDays === 1) return "1 day ago";
  if (diffDays < 30) return `${diffDays} days ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
};

/**
 * Format date to standard locale string
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date string
 */
export const formatDate = (date) => {
  if (!date) return "Never";
  return new Date(date).toLocaleDateString();
};

/**
 * Format rating with stars
 * @param {number} rating - The rating value
 * @param {number} maxRating - Maximum rating (default: 5)
 * @returns {string} - Formatted rating string
 */
export const formatRating = (rating, maxRating = 5) => {
  if (!rating) return "0.0";
  return parseFloat(rating).toFixed(1);
};

/**
 * Get status badge color classes
 * @param {string} status - The status value
 * @returns {string} - CSS classes for status badge
 */
export const getStatusBadgeColor = (status) => {
  switch (status?.toLowerCase()) {
    case "active":
      return "bg-green-100 text-green-800";
    case "closed":
      return "bg-red-100 text-red-800";
    case "disabled":
      return "bg-yellow-100 text-yellow-800";
    case "pending":
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

/**
 * Truncate text with ellipsis
 * @param {string} text - The text to truncate
 * @param {number} maxLength - Maximum length (default: 100)
 * @returns {string} - Truncated text
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

/**
 * Format plugin version
 * @param {string} version - The version string
 * @returns {string} - Formatted version
 */
export const formatVersion = (version) => {
  if (!version) return "Unknown";
  return `v${version}`;
};

/**
 * Get WordPress.org plugin URL
 * @param {string} slug - The plugin slug
 * @returns {string} - WordPress.org plugin URL
 */
export const getWordPressOrgUrl = (slug) => {
  return `https://wordpress.org/plugins/${slug}/`;
};

/**
 * Get plugin icon URL with fallback
 * @param {object} plugin - The plugin object
 * @param {string} size - Preferred size ('1x', '2x', 'svg')
 * @returns {string|null} - Icon URL or null
 */
export const getPluginIconUrl = (plugin, size = "2x") => {
  if (!plugin?.icons) return null;

  // Try preferred size first, then fallback to other sizes
  return (
    plugin.icons[size] ||
    plugin.icons["2x"] ||
    plugin.icons["1x"] ||
    plugin.icons["svg"] ||
    null
  );
};

/**
 * Get plugin banner URL with fallback
 * @param {object} plugin - The plugin object
 * @param {string} size - Preferred size ('high', 'low')
 * @returns {string|null} - Banner URL or null
 */
export const getPluginBannerUrl = (plugin, size = "high") => {
  if (!plugin?.banners) return null;

  return (
    plugin.banners[size] ||
    plugin.banners["high"] ||
    plugin.banners["low"] ||
    null
  );
};
